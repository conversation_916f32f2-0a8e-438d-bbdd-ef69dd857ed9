import { useState } from 'react';
import StepChannels from './components/StepChannels';
import StepKPISetup from '../Personalization/components/StepKPISetup/StepKPISetup';
import { Box, Divider, Flex, Button } from '@chakra-ui/react';
import ProgressBar from './components/ProgressBar';
import { PersonalizationFormData } from './utils/types'; // ✅ import your interface

const steps = ['Channels', 'KPI Setup', 'Review'];

const Personalization = () => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<PersonalizationFormData>({
    selectedChannels: [],
    channelConfigs: {},
  });

  const nextStep = () => setStep((s) => Math.min(s + 1, steps.length));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const renderStep = () => {
    switch (step) {
      case 1:
        return <StepChannels data={formData} setData={setFormData} />;
       case 2:
        return <StepKPISetup data={formData} setData={setFormData} />;
      // case 3:
      //   return <StepReview data={formData} />;
      default:
        return null;
    }
  };

  return (
    <Flex
      direction="column"
      minH="100vh" 
      justify="space-between"
      p={8}
      w="100%"
      pb={20} 
    >
    
      <Box>
        <Box mb={4} fontWeight="medium" fontFamily="Poppins, sans-serif" fontSize="xl">
          Customize Your AI CMO
        </Box>

        <ProgressBar steps={steps} currentStep={step} />
        <Divider mt={4} mb={8} borderColor="#D5D5D5" />

        {renderStep()}
      </Box>

    
      <Box mt={8} pt={4} borderTop="1px solid" borderColor="gray.200">
        <Flex justify="space-between" align="center" mt={4}>
          <Button variant="outline" onClick={prevStep}>
            Back
          </Button>
          <Flex gap={3}>
            <Button variant="ghost">Cancel</Button>
            <Button colorScheme="purple" onClick={nextStep}>
              Next
            </Button>
          </Flex>
        </Flex>
      </Box>
    </Flex>
  );
};

export default Personalization;
