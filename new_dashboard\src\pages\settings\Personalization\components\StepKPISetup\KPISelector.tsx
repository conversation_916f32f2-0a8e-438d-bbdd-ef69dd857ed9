import { Flex, Select, Text } from '@chakra-ui/react';
import { ChannelKPIConfig } from '../../utils/types';

interface KPISelectorProps {
  config: ChannelKPIConfig;
  onChange: (update: Partial<ChannelKPIConfig>) => void;
}

const KPISelector = ({ config, onChange }: KPISelectorProps) => {
  const handleChange = (key: keyof ChannelKPIConfig, value: string) => {
    onChange({ [key]: value } as Partial<ChannelKPIConfig>);
  };

  return (
    <Flex gap={4} mb={6}>
      <Flex direction="column" flex="1">
        <Text fontSize="sm" mb={1}>Primary KPI</Text>
        <Select
          placeholder="Select KPI"
          value={config.primaryKPI}
          onChange={(e) => handleChange('primaryKPI', e.target.value)}
        >
          <option value="Sessions">Sessions</option>
          <option value="Revenue">Revenue</option>
          <option value="CTR">CTR</option>
        </Select>
      </Flex>

      <Flex direction="column" flex="1">
        <Text fontSize="sm" mb={1}>Secondary KPI</Text>
        <Select
          placeholder="Select KPI"
          value={config.secondaryKPI}
          onChange={(e) => handleChange('secondaryKPI', e.target.value)}
        >
          <option value="CVR">CVR</option>
          <option value="ROAS">ROAS</option>
          <option value="Conversions">Conversions</option>
        </Select>
      </Flex>

      <Flex direction="column" flex="1">
        <Text fontSize="sm" mb={1}>Tertiary KPI</Text>
        <Select
          placeholder="Select KPI"
          value={(config as any).tertiaryKPI || ''}
          onChange={(e) => onChange({ tertiaryKPI: e.target.value } as any)}
        >
          <option value="CPA">CPA</option>
          <option value="Impressions">Impressions</option>
        </Select>
      </Flex>
    </Flex>
  );
};

export default KPISelector;
