import React from 'react';
import { <PERSON>ader2, <PERSON><PERSON>ircle2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/utils';

interface FormFieldProps {
   id: string;
   name: string;
   type: string;
   label: string;
   placeholder: string;
   value: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   required?: boolean;
   disabled?: boolean;
   className?: string;
}

interface ApiResponseProps {
   success: boolean;
   message: string;
}

interface CasaDBConnectionPanelProps {
   title: string;
   description: string;
   host: string;
   port: string;
   user: string;
   password: string;
   database: string;
   tablename: string;
   schema?: string;
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
   isLoading: boolean;
   apiResponse: ApiResponseProps | null;
   submitButtonText?: string;
   loadingText?: string;
}

const ModernFormField: React.FC<FormFieldProps> = ({
   id,
   name,
   type,
   label,
   placeholder,
   value,
   onChange,
   required = false,
   disabled = false,
   className,
}) => {
   return (
      <div className={cn('space-y-2', className)}>
         <Label htmlFor={id} className='text-sm font-medium text-gray-700'>
            {label}
            {required && <span className='text-red-500 ml-1'>*</span>}
         </Label>
         <Input
            id={id}
            name={name}
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={onChange}
            required={required}
            disabled={disabled}
            className='h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500'
         />
      </div>
   );
};

const ModernApiResponse: React.FC<{ response: ApiResponseProps }> = ({
   response,
}) => {
   if (!response.message) return null;

   return (
      <Alert
         className={cn(
            'border-l-4',
            response.success
               ? 'border-l-green-500 bg-green-50 text-green-800'
               : 'border-l-red-500 bg-red-50 text-red-800',
         )}
      >
         {response.success ? (
            <CheckCircle2 className='h-4 w-4' />
         ) : (
            <AlertCircle className='h-4 w-4' />
         )}
         <AlertDescription className='font-medium'>
            {response.message}
         </AlertDescription>
      </Alert>
   );
};

export const CasaDBConnectionPanel: React.FC<CasaDBConnectionPanelProps> = ({
   title,
   description,
   host,
   port,
   user,
   password,
   database,
   tablename,
   schema,
   onChange,
   onSubmit,
   isLoading,
   apiResponse,
   submitButtonText = 'Connect',
   loadingText = 'Connecting...',
}) => {
   return (
      <div className='space-y-6 h-full flex flex-col'>
         <div className='space-y-2 flex-shrink-0'>
            <h3 className='text-lg font-semibold text-gray-900'>{title}</h3>
            <p className='text-sm text-gray-600'>{description}</p>
         </div>

         <form onSubmit={onSubmit} className='space-y-6 flex-1 flex flex-col'>
            <div className='space-y-4 flex-1'>
               <ModernFormField
                  id='host'
                  name='host'
                  type='text'
                  label='Host'
                  placeholder='Enter host address (e.g., 20.xxx.xxx.xxx)'
                  value={host}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='port'
                  name='port'
                  type='number'
                  label='Port'
                  placeholder='Enter port (e.g., 9000)'
                  value={port}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='user'
                  name='user'
                  type='text'
                  label='User'
                  placeholder='Enter username'
                  value={user}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='password'
                  name='password'
                  type='password'
                  label='Password'
                  placeholder='Enter password'
                  value={password}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='database'
                  name='database'
                  type='text'
                  label='Database'
                  placeholder='Enter database name (e.g., all)'
                  value={database}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='tablename'
                  name='tablename'
                  type='text'
                  label='Table Name'
                  placeholder='Enter table name'
                  value={tablename}
                  onChange={onChange}
                  required
                  disabled={isLoading}
               />
               <ModernFormField
                  id='schema'
                  name='schema'
                  type='text'
                  label='Schema'
                  placeholder='Enter schema name (optional)'
                  value={schema || ''}
                  onChange={onChange}
                  disabled={isLoading}
               />
            </div>

            <div className='flex-shrink-0 space-y-4'>
               {apiResponse && <ModernApiResponse response={apiResponse} />}

               <Button
                  type='submit'
                  disabled={isLoading || !host || !port || !user || !password}
                  className='w-full h-11 bg-purple-500 hover:bg-purple-700 text-white font-medium'
               >
                  {isLoading ? (
                     <>
                        <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                        {loadingText}
                     </>
                  ) : (
                     <>{submitButtonText}</>
                  )}
               </Button>
            </div>
         </form>
      </div>
   );
};
