import { useState, useEffect } from 'react';
import { Box, Divider, Flex, Text } from '@chakra-ui/react';
import {
  PersonalizationFormData,
  ChannelKPIConfig,
} from '../../utils/types';
import ChannelTabs from '../StepKPISetup/ChannelTabs';
import KPISelector from '../StepKPISetup/KPISelector';
import WeightingSlider from '../StepKPISetup/WeightingSlider';
import BenchmarksTable from '../StepKPISetup/BenchmarksTable';

interface StepKPISetupProps {
  data: PersonalizationFormData;
  setData: (data: PersonalizationFormData) => void;
}

const StepKPISetup = ({ data, setData }: StepKPISetupProps) => {
  const selectedChannels = data.selectedChannels || [];
  console.log('selectedChannels', selectedChannels);
  const channelConfigs = data.channelConfigs || {};
  console.log('channelConfigs', channelConfigs);
  const [activeChannel, setActiveChannel] = useState<string>(
    selectedChannels[0] || ''
  );
  console.log('activeChannel', activeChannel);


  useEffect(() => {
    if (selectedChannels.length > 0 && !activeChannel) {
      setActiveChannel(selectedChannels[0]);
    }
  }, [selectedChannels, activeChannel]);

 
  const updateChannelConfig = (
    channelId: string,
    updates: Partial<ChannelKPIConfig>
  ) => {
    if (!channelId) return;
    const existingConfig: ChannelKPIConfig = data.channelConfigs[channelId] || {
    primaryKPI: '',
    secondaryKPI: '',
    tertiaryKPI: '',
    weighting: { primary: 60, secondary: 30 },
    benchmarks: [], 
  };

  const newConfig: ChannelKPIConfig = {
    ...existingConfig,
    ...updates,
    weighting: {
      ...existingConfig.weighting,
      ...(updates.weighting || {}),
    },
    benchmarks: updates.benchmarks ?? existingConfig.benchmarks ?? [],
  };

  setData({
    ...data,
    channelConfigs: {
      ...data.channelConfigs,
      [channelId]: newConfig,
    },
  });
  };

  
  const activeConfig: ChannelKPIConfig = data.channelConfigs?.[activeChannel] || {
    primaryKPI: '',
    secondaryKPI: '',
    tertiaryKPI: '',
    weighting: { primary: 60, secondary: 30 },
    benchmarks: [],
  };

  
  if (!selectedChannels.length) {
    return (
      <Box mt={10} textAlign="center">
        <Text fontSize="md" color="gray.600">
          No channels selected yet. Please go back and choose at least one channel.
        </Text>
      </Box>
    );
  }

  return (
    <Box>
      
      <Text fontWeight="semibold" mb={2}>
        Channel KPI Setup
      </Text>
      <Text fontSize="sm" color="gray.600" mb={6}>
        Set KPIs, weights, and targets for each selected channel.
      </Text>

     
      <ChannelTabs
        channels={selectedChannels}
        activeChannel={activeChannel}
        onSelect={setActiveChannel}
      />

      <Divider my={6} />

     
      <KPISelector
        config={activeConfig}
        onChange={(update) => updateChannelConfig(activeChannel, update)}
      />

      <WeightingSlider
        weighting={activeConfig.weighting}
        onChange={(update) =>
          updateChannelConfig(activeChannel, { weighting: update })
        }
      />

     
      {activeChannel && (
        <BenchmarksTable
          channelId={activeChannel}
          benchmarks={activeConfig.benchmarks}
          onChange={(b) =>
            updateChannelConfig(activeChannel, { benchmarks: b })
          }
        />
      )}
    </Box>
  );
};

export default StepKPISetup;
