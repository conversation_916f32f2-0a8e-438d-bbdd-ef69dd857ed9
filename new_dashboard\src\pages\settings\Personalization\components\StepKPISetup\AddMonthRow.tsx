import { Tr, Td, Button } from '@chakra-ui/react';
import { Plus } from 'lucide-react';

interface AddMonthRowProps {
  onAdd: () => void;
}

const AddMonthRow = ({ onAdd }: AddMonthRowProps) => (
  <Tr>
    <Td colSpan={6}>
      <Button
        onClick={onAdd}
        variant="ghost"
        size="sm"
        colorScheme="purple"
        leftIcon={<Plus size={14} />}
      >
        Add Month
      </Button>
    </Td>
  </Tr>
);

export default AddMonthRow;
