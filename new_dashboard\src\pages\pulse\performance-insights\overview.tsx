import '../pulse.scss';
import { useEffect, useState } from 'react';
import { useApiQuery } from '../../../hooks/react-query-hooks';
import { Box, Flex, Grid, Heading, Image, Text } from '@chakra-ui/react';
import CustomdropDown from '../../../components/customDropdown/choose-dropdown';
import PerformanceCard from '../components/performance-card';
import pulseService, { Campaign } from '../../../api/service/pulse';
import { UserDetails } from '../components/interface';
import { useAppSelector, useAppDispatch } from '../../../store/store';
import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { componentNames, setFlag } from '../../../store/reducer/tour-reducer';
import { setDynamicObjectives } from '../../../store/reducer/overview-dropdown-reducer';
import {
   setMetric,
   setObjective,
   setChannel,
} from '../../../store/reducer/overview-dropdown-reducer';
import {
   getDropdownChannelOptions,
   dropdownDayOptions,
   dropdownObjectiveOptions,
   objectiveToMetrics,
   StatusTypes,
   getDateRange,
   REQUIRED_KPIS_GOOGLE_ADS,
   REQUIRED_KPIS_META_ADS,
   cap,
} from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import SkeletonLoaderCampaigns from '../../../components/skeletonloader/skeleton-loader-kpi';
import TooltipIcon from '../../../components/info-icon-content/tooltip-message';
import { content } from '../../../components/info-icon-content/info-content';
import OverviewAndTrackedTabs from './overview-tracked-tabs';
import {
   OverviewQueryKeys,
   pulseMetaKeys,
} from '../../dashboard/utils/query-keys';
import pulseMetaService, {
   DaywiseCampaignKPIsCalculated,
} from '../../../api/service/pulse/performance-insights/meta-ads';
import IntegrateInfo from '../../../components/integrate-info/integrate-info';
import { integrationInfoStrings } from '../../../utils/strings/integrate-info-strings';
import GoogleAdsPerformanceCard from '../components/google-campaign-card';
import DateRangeSelect from '../../dashboard/components/date-select';
import nodataImage from '../../../assets/image/pulse/performance-insights/no-data-image.png';
import { splitAndUppercaseString } from '../../marco/utils/alerting-agent/agents-helpers';
import { MdFilterListAlt } from 'react-icons/md';
import { Button } from '@/components/ui/button';
import { Input as TWInput } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Popover,
   PopoverContent,
   PopoverTrigger,
} from '@/components/ui/popover';

type CampaignLike = { campaign_name: string };

const Overview: React.FC = () => {
   const intro = introJs();
   const dispatch = useAppDispatch();

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);
   const { connectionDetails } = useAppSelector((state) => state.media);
   const { performanceInsight } = useAppSelector((state) => state.tour);
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
   const { channel, objective, metric, metricsOptions, dynamicObjectives } =
      useAppSelector((state) => state.dropdown);
   const { start_date, end_date, prev_start_date, prev_end_date, days } =
      getDateRange(dateRange, prevRange);

   const metricsArray = objectiveToMetrics[objective] || [];
   const dropdownChannelOptions = getDropdownChannelOptions(connectionDetails);

   const [filters, setFilters] = useState({
      contains: '',
      doesNotContain: '',
   });

   const filterCampaignsByName = <T extends CampaignLike>(
      campaigns: T[],
      filters: { contains: string; doesNotContain: string },
   ): T[] => {
      const contains = filters.contains.trim().toLowerCase();
      const doesNotContain = filters.doesNotContain.trim().toLowerCase();

      return campaigns.filter((campaign) => {
         const name = campaign.campaign_name.toLowerCase();

         const matchesContains = contains === '' || name.includes(contains);
         const matchesDoesNotContain =
            doesNotContain === '' || !name.includes(doesNotContain);

         return matchesContains && matchesDoesNotContain;
      });
   };

   const sortMetaCampaignsByTracked = (
      campaigns: DaywiseCampaignKPIsCalculated[],
   ) => {
      if (!campaigns) return [];

      const trackedSorted = campaigns.sort((a, b) => {
         const aTracked = trackedCampaigns?.data?.some(
            (campaign) =>
               a.campaign_id === campaign.campaign_id &&
               campaign.kpi_name === metric &&
               campaign.tracked,
         );

         const bTracked = trackedCampaigns?.data?.some(
            (campaign) =>
               b.campaign_id === campaign.campaign_id &&
               campaign.kpi_name === metric &&
               campaign.tracked,
         );

         return Number(bTracked) - Number(aTracked);
      });

      const statusOrder = {
         ACTIVE: 0,
         PAUSED: 1,
      };

      return trackedSorted.sort((a, b) => {
         const statusDiff =
            statusOrder[a.recent_campaign_status] -
            statusOrder[b.recent_campaign_status];

         if (statusDiff !== 0) return statusDiff;

         const aName = a.campaign_name?.trim().toLowerCase() ?? '';
         const bName = b.campaign_name?.trim().toLowerCase() ?? '';
         return aName.localeCompare(bName);
      });
   };

   const sortGoogleCampaignsByTracked = (campaigns: Campaign[]) => {
      return campaigns
         ?.sort((a, b) => {
            const aTracked = a.trackedKPIs.some(
               (kpi) =>
                  kpi.kpi_name === metric &&
                  Number(kpi.campaign_id) === Number(a.campaign_id),
            );

            const bTracked = b.trackedKPIs.some(
               (kpi) =>
                  kpi.kpi_name === metric &&
                  Number(kpi.campaign_id) === Number(b.campaign_id),
            );

            return Number(bTracked) - Number(aTracked);
         })
         ?.sort((a, b) => {
            const statusOrder = {
               [StatusTypes.ENABLED]: 0,
               [StatusTypes.PAUSED]: 1,
               [StatusTypes.REMOVED]: 2,
            };

            const statusDiff =
               statusOrder[a.campaign_status] - statusOrder[b.campaign_status];

            if (statusDiff !== 0) return statusDiff;

            return a.campaign_name.localeCompare(b.campaign_name);
         });
   };

   const {
      data: metaObjectives,
      isLoading: isLoadingMetaObjectives,
      isSuccess: fetchedMetaObjectives,
   } = useApiQuery({
      queryKey: [pulseMetaKeys.metaObjectives, String(channel)],
      queryFn: () => {
         return pulseMetaService.fetchMetaObjectives({
            client_id: userDetails?.client_id,
         });
      },
      enabled: channel === 'meta_ads',
      refetchOnWindowFocus: false,
   });

   const {
      data: googleObjectives,
      isLoading: isLoadingGoogleObjectives,
      isSuccess: fetchedGoogleObjectives,
   } = useApiQuery({
      queryKey: [OverviewQueryKeys.objectives, String(channel)],
      queryFn: () => {
         return pulseService.fetchGoogleAdsObjectives({
            client_id: userDetails?.client_id,
         });
      },
      enabled: channel === 'google_ads',
      refetchOnWindowFocus: false,
   });

   // TODO: Need to fetch metrics for meta ads similar to google ads instead of hardcoding

   const metricsPayload = {
      client_id: userDetails?.client_id,
      channel_type: objective,
   };

   const {
      data: googleMetrics,
      isLoading: isGoogleMetricsLoading,
      isSuccess: fetchedGoogleMetrics,
   } = useApiQuery({
      queryKey: [OverviewQueryKeys?.metrics, String(channel), objective],
      queryFn: () => pulseService.fetchGoogleAdsKpisNames(metricsPayload),
      enabled:
         channel === 'google_ads' &&
         fetchedGoogleObjectives &&
         !!googleObjectives,
      refetchOnWindowFocus: false,
   });

   const metaDaywiseCampaignPayload = {
      client_id: userDetails?.client_id,
      objective: objective,
      kpis: [...metricsArray, ...REQUIRED_KPIS_META_ADS],
      start_date: start_date,
      end_date: end_date,
      prev_start_date: prev_start_date,
      prev_end_date: prev_end_date,
   };

   const {
      data: metaCampaigns,
      isFetching: isMetaCampaignsFetching,
      isSuccess: fetchedMetaCampaigns,
   } = useApiQuery({
      queryKey: [
         pulseMetaKeys.metaCampaigns,
         metric,
         objective,
         String(channel),
         JSON.stringify(dateRange),
         JSON.stringify(prevRange),
      ],
      queryFn: () => {
         return pulseMetaService.fetchMetaDaywiseCampaigns(
            metaDaywiseCampaignPayload,
         );
      },
      enabled: channel === 'meta_ads' && !!metric && !!objective,
      refetchOnWindowFocus: false,
   });

   const GoogleKpisNames = metricsOptions?.map((option) => option.value);
   const googleadsDaywiseCampaignPayload = {
      client_id: userDetails?.client_id,
      channel_type: objective,
      kpis: [...GoogleKpisNames, ...REQUIRED_KPIS_GOOGLE_ADS],
      start_date: start_date,
      end_date: end_date,
      prev_start_date: prev_start_date,
      prev_end_date: prev_end_date,
   };

   const {
      data: googleCampaignsData,
      isFetching: isGoogleCampaignsFetching,
      isSuccess: fetchedGoogleCampaigns,
   } = useApiQuery({
      queryKey: [
         OverviewQueryKeys?.daywiseCampaign,
         metric,
         objective,
         String(channel),
         JSON.stringify(dateRange),
         JSON.stringify(prevRange),
      ],
      queryFn: () => {
         return pulseService.fetchGoogleAdsCampaignDaywise(
            googleadsDaywiseCampaignPayload,
         );
      },
      enabled:
         channel === 'google_ads' && fetchedGoogleMetrics && !!googleMetrics,
      refetchOnWindowFocus: false,
   });

   const googleCampaigns: Campaign[] | null =
      channel === 'google_ads'
         ? ((googleCampaignsData?.[0]
              ?.fn_googleads_campaign_daywise_with_multi_kpi_value_get as Campaign[]) ??
           null)
         : null;

   const {
      data: trackedCampaigns,
      isFetching: isTrackedCampaignKPIsFetching,
      isSuccess: fetchedTrackedCampaignKPIs,
   } = useApiQuery({
      queryKey: [
         pulseMetaKeys.trackedCampaigns,
         channel,
         metric,
         objective,
         JSON.stringify(dateRange),
         JSON.stringify(prevRange),
      ],
      queryFn: () => {
         return pulseMetaService.fetchMetaTrackedCampaignKPIs({
            client_id: userDetails?.client_id,
         });
      },
      enabled: !!channel && !!metric && !!objective,
      refetchOnWindowFocus: false,
   });

   const handleChannelSelect = (value: string) => {
      dispatch(setChannel(value));
   };

   const handleObjectiveSelect = (value: string) => {
      dispatch(setObjective(value));
   };

   const handleMetricSelect = (value: string) => {
      dispatch(setMetric(value));
   };

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
      available: boolean;
   }[] = [
      {
         element: '#overview',
         intro: 'Get an overview of your ads from Campaign to ad level.',
         position: 'top',
         available: dropdownChannelOptions?.length > 0,
      },
      {
         element: '#tracked',
         intro: 'All your pinned KPIs will be placed here.',
         position: 'top',
         available: dynamicObjectives?.length > 0,
      },
      {
         element: '#performanceMeta',
         intro: 'Choose platform here to view the performance insights.',
         position: 'top',
         available: dropdownChannelOptions?.length > 0,
      },
      {
         element: '#performanceSlaesId',
         intro: 'Choose objectives here to view the performance insights.',
         position: 'top',
         available: dropdownObjectiveOptions?.length > 0,
      },

      {
         element: '#performanceRoas',
         intro: 'Choose KPIs here to view the performance insights.',
         position: 'top',
         available: metricsOptions?.length > 0,
      },
      {
         element: '#performanceDays',
         intro: 'Select the time frame for your analysis.',
         position: 'top',
         available: dropdownDayOptions?.length > 0,
      },
      {
         element: `${channel === 'google_ads' ? '#googleViewId' : '#overview'}`,
         intro: 'View details for in-depth insights.',
         position: 'top',
         available:
            channel === 'google_ads'
               ? !!googleCampaigns
               : !!metaCampaigns?.data,
      },

      {
         element: `${channel === 'google_ads' ? '#googleTrackBtnId' : '#performanceTrackBtnId'}`,
         intro: 'Track KPIs that you would closely like to monitor.',
         position: 'top',
         available:
            channel === 'google_ads'
               ? !!googleCampaigns
               : !!metaCampaigns?.data,
      },
   ];

   const startTour = () => {
      const availableSteps = steps.filter((step) => step.available);

      if (availableSteps.length > 0) {
         intro.setOptions({ steps: availableSteps });

         void intro.start();

         dispatch(
            setFlag({
               componentName: componentNames.PERFORMANCE_INISGHT,
               flag: false,
            }),
         );
      } else {
         alert('No data available to start the tour.');
      }
   };

   useEffect(() => {
      if (
         channel === 'meta_ads' &&
         fetchedMetaObjectives &&
         metaObjectives?.data
      ) {
         dispatch(setDynamicObjectives(metaObjectives.data));
      }

      if (
         channel === 'google_ads' &&
         fetchedGoogleObjectives &&
         googleObjectives?.[0]?.fn_get_client_GoogleAdsObjectives
      ) {
         dispatch(
            setDynamicObjectives(
               googleObjectives[0].fn_get_client_GoogleAdsObjectives,
            ),
         );
      }
   }, [
      fetchedMetaObjectives,
      fetchedGoogleObjectives,
      channel,
      metaObjectives,
      googleObjectives,
      dispatch,
   ]);

   useEffect(() => {
      const dataAvailable = isDataAvailable();

      if (performanceInsight && dataAvailable) {
         console.log('Data available. Starting tour...');
         startTour();
      } else {
         console.log('Data unavailable. Tour will not start.');
      }
   }, [
      performanceInsight,
      dropdownChannelOptions,
      dropdownObjectiveOptions,
      metricsOptions,
      dropdownDayOptions,
      dynamicObjectives,
   ]);

   const isDataAvailable = () => {
      return (
         dropdownChannelOptions?.length > 0 ||
         dropdownObjectiveOptions?.length > 0 ||
         metricsOptions?.length > 0 ||
         dropdownDayOptions?.length > 0 ||
         dynamicObjectives?.length > 0
      );
   };

   useEffect(() => {
      dispatch(setChannel(dropdownChannelOptions[0].value));
   }, []);

   if (!optimisationsStatus.complete && !optimisationsStatus.ads_account) {
      return (
         <IntegrateInfo
            feature={integrationInfoStrings.performanceInsights.title}
            text={integrationInfoStrings.performanceInsights.description}
         />
      );
   }

   return (
      <Box height='100%' p='20px 40px'>
         <Box mb={6}>
            <Flex align='center'>
               <Heading as='h4' size='lg' fontWeight='500'>
                  Performance Insights
               </Heading>
               <TooltipIcon
                  label={content.performance}
                  placement='top'
                  iconColor='#7F56D9' // changes icon color
                  ml={2}
                  mt={2}
               />
            </Flex>
         </Box>
         <OverviewAndTrackedTabs type='perf' />
         <Flex
            wrap='wrap'
            gap={6}
            justify={{ base: 'center', md: 'space-between' }}
            align='center'
         >
            <Flex
               mb={6}
               gap={4}
               wrap='wrap'
               flex={{ base: '1 0 100%', md: '1 0 auto' }}
               justify={{ base: 'center', md: 'flex-start' }}
            >
               {dropdownChannelOptions && (
                  <CustomdropDown
                     id='performanceMeta'
                     options={dropdownChannelOptions}
                     onSelect={handleChannelSelect}
                     initialValue={channel}
                     width={114}
                  />
               )}
               {dropdownObjectiveOptions && (
                  <CustomdropDown
                     id='performanceSlaesId'
                     options={dynamicObjectives}
                     onSelect={handleObjectiveSelect}
                     initialValue={objective}
                     width={114}
                  />
               )}
               {metricsOptions && (
                  <CustomdropDown
                     id='performanceRoas'
                     options={metricsOptions?.map((x) => {
                        const a = { value: x.value, label: cap(x.label) };
                        return a;
                     })}
                     onSelect={handleMetricSelect}
                     initialValue={metric}
                     width={125}
                  />
               )}
               <Popover>
                  <PopoverTrigger>
                     <Button variant='outline'>
                        <MdFilterListAlt />
                     </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-80'>
                     <div className='grid gap-4'>
                        <div className='grid gap-2'>
                           <div className='grid grid-cols-3 items-center gap-4'>
                              <Label htmlFor='contains'>Contains</Label>
                              <TWInput
                                 id='contains'
                                 value={filters.contains}
                                 onChange={(e) =>
                                    setFilters({
                                       ...filters,
                                       contains: e.target.value,
                                    })
                                 }
                                 className='col-span-2 h-8'
                              />
                           </div>
                           <div className='grid grid-cols-3 items-center gap-4'>
                              <Label htmlFor='doesNotContain'>
                                 Does not contain
                              </Label>
                              <TWInput
                                 id='doesNotContain'
                                 value={filters.doesNotContain}
                                 onChange={(e) =>
                                    setFilters({
                                       ...filters,
                                       doesNotContain: e.target.value,
                                    })
                                 }
                                 className='col-span-2 h-8'
                              />
                           </div>
                        </div>
                     </div>
                  </PopoverContent>
               </Popover>
            </Flex>
            <Flex
               gap={2}
               align='center'
               flex={{ base: '1 0 100%', md: '0 1 auto' }}
               justify={{ base: 'flex-end', md: 'flex-end' }}
            >
               <DateRangeSelect groupByEnabled={false} pulse />
            </Flex>
         </Flex>
         {(isGoogleCampaignsFetching ||
            isMetaCampaignsFetching ||
            isTrackedCampaignKPIsFetching ||
            isLoadingMetaObjectives ||
            isLoadingGoogleObjectives ||
            isGoogleMetricsLoading) && (
            <SkeletonLoaderCampaigns spacing={10} length={9} />
         )}
         {((channel === 'meta_ads' &&
            fetchedMetaCampaigns &&
            !metaCampaigns?.data) ||
            (channel === 'google_ads' &&
               fetchedGoogleCampaigns &&
               !googleCampaigns)) && (
            <Flex
               height='80%'
               flexDirection='column'
               alignItems='center'
               justifyContent='center'
            >
               <Image src={nodataImage} />
               <Text>
                  No data is available for{' '}
                  <span
                     style={{ fontWeight: 'bold', textTransform: 'uppercase' }}
                  >
                     {splitAndUppercaseString(metric)}
                  </span>{' '}
                  on{' '}
                  <span style={{ fontWeight: 'bold' }}>
                     {splitAndUppercaseString(channel)}
                  </span>{' '}
                  in the past <span style={{ fontWeight: 'bold' }}>{days}</span>{' '}
                  days.
               </Text>
               <Text>Please verify integration or adjust the date range.</Text>
            </Flex>
         )}
         {channel === 'meta_ads' ? (
            <Box>
               {fetchedTrackedCampaignKPIs &&
                  fetchedMetaCampaigns &&
                  metaCampaigns?.data && (
                     <Grid templateColumns='repeat(auto-fill, 450px)' gap={6}>
                        {sortMetaCampaignsByTracked(
                           filterCampaignsByName(metaCampaigns?.data, filters),
                        ).map((campaign, index) => {
                           const tracked =
                              trackedCampaigns?.data?.some(
                                 (x) =>
                                    x.campaign_id === campaign.campaign_id &&
                                    x.kpi_name === metric,
                              ) || false;

                           return (
                              <Box key={index} justifySelf='start'>
                                 <PerformanceCard
                                    overview='overview'
                                    tracked={tracked}
                                    campaign={campaign}
                                    performanceTrackBtnId='performanceTrackBtnId'
                                 />
                              </Box>
                           );
                        })}
                     </Grid>
                  )}
            </Box>
         ) : (
            <Box>
               {!isGoogleCampaignsFetching &&
                  fetchedTrackedCampaignKPIs &&
                  fetchedGoogleCampaigns &&
                  !isGoogleMetricsLoading &&
                  googleCampaigns && (
                     <Grid templateColumns='repeat(auto-fill, 450px)' gap={6}>
                        {sortGoogleCampaignsByTracked(
                           filterCampaignsByName(googleCampaigns, filters),
                        ).map((campaign, index) => {
                           const tracked =
                              campaign?.trackedKPIs.some(
                                 (kpi) =>
                                    kpi.kpi_name === metric &&
                                    Number(kpi.campaign_id) ===
                                       Number(campaign.campaign_id),
                              ) || false;
                           return (
                              <Box key={index} justifySelf='start'>
                                 <GoogleAdsPerformanceCard
                                    googleViewId='googleViewId'
                                    TrackBtnIdG='googleTrackBtnId'
                                    data={campaign}
                                    allcampaign={googleCampaigns}
                                    tracked={tracked}
                                 />
                              </Box>
                           );
                        })}
                     </Grid>
                  )}
            </Box>
         )}
      </Box>
   );
};

export default Overview;
