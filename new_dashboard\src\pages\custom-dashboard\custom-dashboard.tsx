import customDashboardAP<PERSON>, { Conditions } from '@/api/service/custom-dashboard';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import {
   Dialog,
   DialogClose,
   DialogContent,
   DialogFooter,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuLabel,
   DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { useAppSelector } from '@/store/store';
import { AuthUser } from '@/types/auth';
import { PiExportBold } from 'react-icons/pi';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import React, {
   useCallback,
   useEffect,
   useMemo,
   useRef,
   useState,
} from 'react';
import { PiPlusCircleBold, PiMinusCircleBold } from 'react-icons/pi';
import {
   AllCommunityModule,
   ColDef,
   ColGroupDef,
   ModuleRegistry,
   ValueGetterParams,
} from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import DateRangeSelect from '../dashboard/components/date-select';
import EntityFields from './components/entity-fields';
import { Button } from '@/components/ui/button';
import { IconButton, Spinner, Tooltip } from '@chakra-ui/react';
import {
   aggregateKPIsByConfig,
   DataRow,
   HEADER_COLUMNS,
} from './utils/custom-dashboard-helpers';
import { IconType } from 'react-icons';
import { toast } from 'sonner';
import './custom-dashboard.css';
import { dropdownObjectiveOptions } from '../pulse/utils/helper';
import Config from '@/config';
import { useNavigate } from 'react-router-dom';

// Register ag-grid community modules
ModuleRegistry.registerModules([AllCommunityModule]);

const CHANNEL_MAP = {
   meta_ads: 'Meta Ads',
   google_ads: 'Google Ads',
} as const;

interface Form {
   channel: string;
   campaign: Conditions[];
   adset: Conditions[];
}

const META_ADS_AGG_KEYS = ['objective', 'campaign_name', 'adset_name'];

type RowRecord = Record<string, string | number | undefined>;

const formatHeader = (field: string) =>
   HEADER_COLUMNS[field as keyof typeof HEADER_COLUMNS]
      ? HEADER_COLUMNS[field as keyof typeof HEADER_COLUMNS]
      : field.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());

const CustomDashboard: React.FC = () => {
   const navigate = useNavigate();

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const { connectionDetails } = useAppSelector((s) => s.media);
   const { dateRange } = useAppSelector((s) => s.kpi);
   const { aggregatedColumns, visibleColumns } = useAppSelector((s) => s.marco);

   const gridRef = useRef<AgGridReact<DataRow>>(null);
   const dropdownRef = useRef<HTMLDivElement>(null);

   const [form, setForm] = useState<Form>({
      channel: 'meta_ads',
      campaign: [],
      adset: [],
   });

   const [rowData, setRowData] = useState<RowRecord[]>([]);
   const [colDefs, setColDefs] = useState<(ColDef | ColGroupDef)[]>([]);
   const [open, setOpen] = useState(false);
   const [dropdownOpen, setDropdownOpen] = useState(false);

   const [aggregationCols, setAggregationCols] = useState({
      enabled: aggregatedColumns.enabled || [],
      disabled: aggregatedColumns.disabled || [],
   });
   const [visibleCols, setVisibleCols] = useState({
      enabled: visibleColumns.enabled || [],
      disabled: visibleColumns.disabled || [],
   });

   useEffect(() => {
      const handleClickOutside = (e: MouseEvent) => {
         if (
            dropdownRef.current &&
            !dropdownRef.current.contains(e.target as Node)
         ) {
            setDropdownOpen(false);
         }
      };
      if (dropdownOpen)
         document.addEventListener('mousedown', handleClickOutside);
      return () =>
         document.removeEventListener('mousedown', handleClickOutside);
   }, [dropdownOpen]);

   const CHANNELS = useMemo(() => {
      if (!Array.isArray(connectionDetails)) return [];
      return connectionDetails
         .filter(
            (item) =>
               (item.channel_name === 'facebookads' &&
                  item.is_active === true) ||
               (item.channel_name === 'googleads' && item.is_active === true),
         )
         .map((item) =>
            item.channel_name === 'facebookads'
               ? 'meta_ads'
               : item.channel_name === 'googleads'
                 ? 'google_ads'
                 : item.channel_name,
         );
   }, [connectionDetails]);

   // Fetch custom dashboard settings
   const { data: customDashboardSettings, refetch: refetchSettings } =
      useApiQuery({
         queryKey: ['customDashboardSettings', form.channel],
         queryFn: async () =>
            await customDashboardAPI.getCustomDashboardSettings({
               client_id: client_id as string,
               user_id: user_id as string,
               channel: form.channel,
            }),
         enabled: !!form.channel,
         refetchOnMount: false,
         refetchOnWindowFocus: false,
         refetchOnReconnect: false,
      });

   const updateSettingsMutation = useApiMutation({
      queryKey: ['updateSettings', form.channel],
      mutationFn: customDashboardAPI.updateCustomDashboardSettings,
   });

   // Fetch dashboard data
   const { data: customDashboardData, isFetching: isDataFetching } =
      useApiQuery({
         queryKey: [
            'customDashboardData',
            form.channel,
            JSON.stringify(dateRange),
            JSON.stringify(customDashboardSettings?.[0]?.campaign),
            JSON.stringify(customDashboardSettings?.[0]?.adset),
         ],
         queryFn: async () =>
            await customDashboardAPI.getCustomDashboardData({
               client_id: client_id as string,
               user_id: user_id as string,
               channel: form.channel,
               settings: {
                  campaign: customDashboardSettings?.[0]?.campaign as Record<
                     string,
                     Conditions
                  >,
                  adset: customDashboardSettings?.[0]?.adset as Record<
                     string,
                     Conditions
                  >,
               },
               start_date: dateRange.start.split('T')[0],
               end_date: dateRange.end.split('T')[0],
            }),
         enabled: !!form.channel,
         refetchOnMount: false,
         refetchOnWindowFocus: false,
         refetchOnReconnect: false,
      });

   const defaultAggCols = useMemo(() => {
      const campaignKeys = customDashboardSettings?.[0]
         ? Object.keys(customDashboardSettings[0].campaign || {})
         : [];
      const adsetKeys = customDashboardSettings?.[0]
         ? Object.keys(customDashboardSettings[0].adset || {})
         : [];
      return [...campaignKeys, ...META_ADS_AGG_KEYS, ...adsetKeys];
   }, [customDashboardSettings]);

   useEffect(() => {
      if (!customDashboardSettings || customDashboardSettings.length === 0)
         return;
      const s = customDashboardSettings[0];
      setForm({
         channel: s.channel_name || '',
         campaign: Object.values(s.campaign || {}),
         adset: Object.values(s.adset || {}),
      });
      setAggregationCols((prev) => {
         if (prev.enabled.length === 0 && prev.disabled.length === 0) {
            return { enabled: defaultAggCols, disabled: [] };
         }
         return prev;
      });
   }, [customDashboardSettings, defaultAggCols]);

   // Aggregate rows
   const aggregatedRows = useMemo(() => {
      if (!customDashboardData || !customDashboardSettings) return [];

      const aggCols =
         aggregationCols.enabled.length > 0
            ? aggregationCols.enabled
            : defaultAggCols;

      const dataObj = aggregateKPIsByConfig(
         customDashboardData,
         {
            campaign: customDashboardSettings?.[0]?.campaign,
            adset: customDashboardSettings?.[0]?.adset,
         },
         aggCols,
      );

      return Object.entries(dataObj).map(([key, values]) => {
         const parts = key.split(';');
         const dimensions: Record<string, string> = {};
         parts.forEach((p) => {
            const [field, value] = p.split(':');
            if (field && value !== undefined) dimensions[field] = value;
         });
         return { ...dimensions, ...values } as RowRecord;
      });
   }, [
      customDashboardData,
      customDashboardSettings,
      aggregationCols.enabled,
      defaultAggCols,
   ]);

   const derivedAllFields = useMemo(() => {
      if (!aggregatedRows || aggregatedRows.length === 0) return [];
      const seen = new Set<string>();
      const fields: string[] = [];
      aggregatedRows.forEach((r) => {
         Object.keys(r).forEach((k) => {
            if (k && !seen.has(k)) {
               seen.add(k);
               fields.push(k);
            }
         });
      });
      return fields;
   }, [aggregatedRows]);

   // Sync visible columns with data fields
   useEffect(() => {
      const all = derivedAllFields;
      if (!all || all.length === 0) {
         setVisibleCols({ enabled: [], disabled: [] });
         return;
      }
      setVisibleCols((prev) => {
         if (prev.enabled.length === 0 && prev.disabled.length === 0) {
            return { enabled: all, disabled: [] };
         }
         const enabled = prev.enabled.filter((f) => all.includes(f));
         const disabled = prev.disabled.filter((f) => all.includes(f));
         const existingSet = new Set([...enabled, ...disabled]);
         const newOnes = all.filter((f) => !existingSet.has(f));
         return { enabled: [...enabled, ...newOnes], disabled };
      });
   }, [derivedAllFields]);

   // Build AG Grid columns
   useEffect(() => {
      if (!derivedAllFields || derivedAllFields.length === 0) {
         setColDefs([]);
         setRowData([]);
         return;
      }

      const currencyCode =
         (
            customDashboardData?.[0]?.currency
         )?.toUpperCase() || 'INR'; // fallback if missing

      const defs: ColDef<DataRow>[] = derivedAllFields.map((field) => {
         // Adjust header name for currency fields
         let headerName = formatHeader(field);
         if (['total_spent', 'cpm', 'cpl'].includes(field)) {
            headerName += ` (in ${currencyCode})`;
         }

         return {
            field,
            headerName,
            sortable: true,
            filter: true,
            resizable: true,
            suppressSizeToFit: true,
            headerClass: 'ag-header-bold',
            hide: !visibleCols.enabled.includes(field),
            valueGetter: (params: ValueGetterParams<DataRow>) => {
               const data = params.data;
               if (!data) return null;

               if (field === 'objective') {
                  const match = dropdownObjectiveOptions.find(
                     (o) => o.value === data.objective,
                  );
                  return match?.label ?? data.objective ?? null;
               }

               return data[field as keyof DataRow] ?? null;
            },
         } as ColDef<DataRow>;
      });

      setColDefs(defs);
      setRowData(aggregatedRows);
   }, [derivedAllFields, visibleCols.enabled, aggregatedRows]);

   // Handlers
   const handleSelectChange = useCallback((name: keyof Form, value: string) => {
      if (name === 'channel') setForm((prev) => ({ ...prev, channel: value }));
   }, []);

   const handleEntityChange = useCallback(
      (
         level: 'campaign' | 'adset',
         index: number,
         field: string,
         value: string,
      ) => {
         setForm((prev) => {
            const updated = [...prev[level]];
            const orig = updated[index] ?? {
               column: '',
               values: [],
               excludes: [],
               match_type: 'strict',
            };
            updated[index] = {
               ...orig,
               [field]:
                  field === 'values' || field === 'excludes'
                     ? value
                        ? value.split(',').map((v) => v.trim())
                        : []
                     : value,
            };
            return { ...prev, [level]: updated };
         });
      },
      [],
   );

   const addNewEntity = useCallback((level: 'campaign' | 'adset') => {
      setForm((prev) => ({
         ...prev,
         [level]: [
            ...prev[level],
            {
               column: '',
               values: [],
               excludes: [],
               match_type: 'strict',
            },
         ],
      }));
   }, []);

   const removeEntity = useCallback(
      (level: 'campaign' | 'adset', index: number) => {
         setForm((prev) => ({
            ...prev,
            [level]: prev[level].filter((_, i) => i !== index),
         }));
      },
      [],
   );

   const onBtnExport = useCallback(() => {
      const gridApi = gridRef.current?.api;
      if (!gridApi) return;

      // Get selected row count
      const selectedNodes = gridApi.getSelectedNodes();
      const selectedCount = selectedNodes.length;

      // Get selected cell ranges (for range selection mode)
      const selectedRanges = gridApi.getCellRanges?.() || [];

      if (selectedCount > 0 || selectedRanges.length > 0) {
         // ✅ Export only selected rows/cells
         gridApi.exportDataAsCsv({
            onlySelected: true,
         });
      } else {
         // ✅ Export all data
         gridApi.exportDataAsCsv();
      }
   }, []);

   const handleEnableDisableAggColumn = (col: string, enable: boolean) => {
      if (!enable && aggregationCols.enabled.length === 1) {
         toast.error('At least one column is required for aggregation.');
         return;
      }

      if (enable) {
         setAggregationCols((prev) => ({
            enabled: [...prev.enabled, col],
            disabled: prev.disabled.filter((x) => x !== col),
         }));
      } else {
         setAggregationCols((prev) => ({
            enabled: prev.enabled.filter((x) => x !== col),
            disabled: [...prev.disabled, col],
         }));
      }
   };

   const handleEnableDisableVisColumn = useCallback(
      (column: string, enable: boolean) => {
         setVisibleCols((prev) => {
            if (enable) {
               if (prev.enabled.includes(column)) return prev;
               return {
                  enabled: [...prev.enabled, column],
                  disabled: prev.disabled.filter((c) => c !== column),
               };
            } else {
               if (prev.disabled.includes(column)) return prev;
               return {
                  disabled: [...prev.disabled, column],
                  enabled: prev.enabled.filter((c) => c !== column),
               };
            }
         });
      },
      [],
   );

   const updateSettings = useCallback(async () => {
      try {
         const requiredFields = ['column', 'values'];

         const isCampaignValid = form.campaign.every(
            (cond) =>
               cond &&
               requiredFields.every(
                  (key) =>
                     cond[key as keyof typeof cond] !== undefined &&
                     cond[key as keyof typeof cond] !== '' &&
                     cond[key as keyof typeof cond] !== null &&
                     cond[key as keyof typeof cond].length > 0,
               ),
         );

         const isAdsetValid = form.adset.every(
            (cond) =>
               cond &&
               requiredFields.every(
                  (key) =>
                     cond[key as keyof typeof cond] !== undefined &&
                     cond[key as keyof typeof cond] !== '' &&
                     cond[key as keyof typeof cond] !== null &&
                     cond[key as keyof typeof cond].length > 0,
               ),
         );

         if (!isCampaignValid || !isAdsetValid) {
            toast.error('Missing Required Fields');
            return;
         }

         setOpen(false);
         const payload = {
            client_id: client_id as string,
            user_id: user_id as string,
            channel: form.channel,
            settings: {
               campaign: form.campaign.reduce(
                  (acc, condition) => {
                     if (!condition?.column) return acc;
                     acc[condition.column] = {
                        ...condition,
                     };
                     return acc;
                  },
                  {} as Record<string, Conditions>,
               ),
               adset: form.adset.reduce(
                  (acc, condition) => {
                     if (!condition?.column) return acc;
                     acc[condition.column] = {
                        ...condition,
                     };
                     return acc;
                  },
                  {} as Record<string, Conditions>,
               ),
            },
         };
         await updateSettingsMutation.mutateAsync(payload);
         await refetchSettings();
      } catch (err) {
         console.error('Failed to update settings', err);
      }
   }, [client_id, user_id, form, updateSettingsMutation, refetchSettings]);

   const Section: React.FC<{
      title: string;
      items: string[];
      onClick: (value: string) => void;
      iconColor: string;
      Icon: IconType;
   }> = ({ title, items, onClick, iconColor, Icon }) => (
      <div className='mb-3'>
         <DropdownMenuLabel className='text-md font-bold text-gray-700 mb-1'>
            {title}
         </DropdownMenuLabel>
         <DropdownMenuGroup className='space-y-1'>
            {items.map((col) => (
               <DropdownMenuItem
                  key={col}
                  className='text-sm px-2 py-1 rounded-md hover:bg-gray-50 flex justify-between items-center'
               >
                  <span className='truncate text-gray-800'>
                     {formatHeader(col)}
                  </span>
                  <span
                     onClick={(e) => {
                        e.stopPropagation();
                        onClick(col);
                     }}
                     className='cursor-pointer hover:opacity-80 text-base'
                  >
                     <Icon className={`${iconColor}`} />
                  </span>
               </DropdownMenuItem>
            ))}
         </DropdownMenuGroup>
      </div>
   );

   useEffect(() => {
      if (
         Config.VITE_XI_AGENT_API !== 'https://xi-agent.flable.ai' ||
         client_id !== 'A01071'
      ) {
         navigate('/');
      }
   });

   return (
      <div className='w-full h-full p-5'>
         <div className='text-xl font-bold mb-5'>Custom Dashboard</div>

         <div className='w-full grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
            <div className='w-full grid col-span-1 grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
               <div className='flex flex-col gap-1 col-span-1'>
                  <Label
                     htmlFor='channel'
                     className='text-[14px] ml-1 font-semibold gap-1'
                  >
                     Channel<span className='text-red-500'>*</span>
                  </Label>
                  <Select
                     value={form.channel}
                     onValueChange={(val) => handleSelectChange('channel', val)}
                  >
                     <SelectTrigger className='w-full !h-[40px] cursor-pointer focus-visible:ring-0 focus-visible:outline-none'>
                        <SelectValue placeholder='Select channel' />
                     </SelectTrigger>
                     <SelectContent className='bg-white'>
                        {CHANNELS.map((channel) => (
                           <SelectItem key={channel} value={channel}>
                              {CHANNEL_MAP[
                                 channel as keyof typeof CHANNEL_MAP
                              ] ?? channel}
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
               </div>

               <div className='flex flex-col gap-1 col-span-1'>
                  <Label
                     htmlFor='date-range'
                     className='text-[14px] ml-1 font-semibold gap-1'
                  >
                     Date Range<span className='text-red-500'>*</span>
                  </Label>
                  <DateRangeSelect groupByEnabled={false} pulse />
               </div>
            </div>

            <div className='flex justify-between gap-1 col-span-1'>
               <Dialog open={open} onOpenChange={setOpen}>
                  <DialogTrigger asChild>
                     <Button className='mt-6 max-w-[100px] !h-[40px] bg-[#7F56D9]'>
                        Settings
                     </Button>
                  </DialogTrigger>

                  <DialogContent className='min-w-[90%] md:min-w-[80%] bg-white'>
                     <DialogHeader>
                        <DialogTitle className='font-bold'>
                           Edit Settings
                        </DialogTitle>
                     </DialogHeader>

                     <div className='flex justify-start items-center gap-4 mt-2 mb-2'>
                        <div className='w-[140px] text-[16px] font-semibold'>
                           Campaign Level
                        </div>
                        <Button
                           size='sm'
                           onClick={() => addNewEntity('campaign')}
                           className='bg-[#7F56D9]'
                        >
                           Add Entity
                        </Button>
                     </div>

                     {form.campaign.length > 0 ? (
                        form.campaign.map((campaign, idx) => (
                           <EntityFields
                              key={`campaign-${idx}`}
                              name={campaign.column}
                              values={(campaign.values || []).join(',')}
                              excludes={(campaign.excludes || []).join(',')}
                              match_type={campaign.match_type}
                              onChange={(field, value) =>
                                 handleEntityChange(
                                    'campaign',
                                    idx,
                                    field,
                                    value,
                                 )
                              }
                              onRemove={() => removeEntity('campaign', idx)}
                           />
                        ))
                     ) : (
                        <p className='text-gray-500 text-sm ml-[140px]'>
                           No campaign entities added.
                        </p>
                     )}

                     <div className='flex justify-start items-center gap-4 mt-2 mb-2'>
                        <div className='w-[140px] text-[16px] font-semibold'>
                           Adset Level
                        </div>
                        <Button
                           size='sm'
                           onClick={() => addNewEntity('adset')}
                           className='bg-[#7F56D9]'
                        >
                           Add Entity
                        </Button>
                     </div>

                     {form.adset.length > 0 ? (
                        form.adset.map((adset, idx) => (
                           <EntityFields
                              key={`adset-${idx}`}
                              name={adset.column}
                              values={(adset.values || []).join(',')}
                              excludes={(adset.excludes || []).join(',')}
                              match_type={adset.match_type}
                              onChange={(field, value) =>
                                 handleEntityChange('adset', idx, field, value)
                              }
                              onRemove={() => removeEntity('adset', idx)}
                           />
                        ))
                     ) : (
                        <p className='text-gray-500 text-sm ml-[140px]'>
                           No adset entities added.
                        </p>
                     )}

                     <DialogFooter className='mt-4'>
                        <DialogClose asChild>
                           <Button
                              variant='outline'
                              onClick={() => void setOpen(false)}
                           >
                              Cancel
                           </Button>
                        </DialogClose>
                        <Button
                           onClick={() => void updateSettings()}
                           className='bg-[#7F56D9]'
                        >
                           Save Settings
                        </Button>
                     </DialogFooter>
                  </DialogContent>
               </Dialog>

               <div className='flex gap-2 items-center'>
                  <div className='mt-2'>
                     <DropdownMenu
                        open={dropdownOpen}
                        onOpenChange={setDropdownOpen}
                     >
                        <DropdownMenuTrigger>
                           <Button
                              variant='outline'
                              className='w-[150px] h-[40px]'
                              onClick={() => setDropdownOpen((prev) => !prev)}
                           >
                              Column Config
                           </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                           ref={dropdownRef}
                           className='bg-white w-[450px] p-2'
                        >
                           <DropdownMenuLabel className='text-md font-semibold text-gray-800'>
                              Manage Columns
                           </DropdownMenuLabel>

                           <div className='flex gap-2 justify-between'>
                              <div>
                                 <Section
                                    title='Enabled'
                                    items={aggregationCols.enabled}
                                    onClick={(val) =>
                                       handleEnableDisableAggColumn(val, false)
                                    }
                                    iconColor='text-red-500'
                                    Icon={PiMinusCircleBold}
                                 />
                                 <Section
                                    title='Disabled'
                                    items={aggregationCols.disabled}
                                    onClick={(val) =>
                                       handleEnableDisableAggColumn(val, true)
                                    }
                                    iconColor='text-green-500'
                                    Icon={PiPlusCircleBold}
                                 />
                              </div>

                              <div>
                                 <Section
                                    title='Enabled'
                                    items={visibleCols.enabled}
                                    onClick={(val) =>
                                       handleEnableDisableVisColumn(val, false)
                                    }
                                    iconColor='text-red-500'
                                    Icon={PiMinusCircleBold}
                                 />
                                 <Section
                                    title='Disabled'
                                    items={visibleCols.disabled}
                                    onClick={(val) =>
                                       handleEnableDisableVisColumn(val, true)
                                    }
                                    iconColor='text-green-500'
                                    Icon={PiPlusCircleBold}
                                 />
                              </div>
                           </div>
                        </DropdownMenuContent>
                     </DropdownMenu>
                  </div>

                  <div className='mt-2'>
                     <Tooltip label='Export as CSV'>
                        <IconButton
                           className='cursor-pointer'
                           onClick={onBtnExport}
                           aria-label='Download CSV export file'
                           icon={<PiExportBold />}
                        />
                     </Tooltip>
                  </div>
               </div>
            </div>
         </div>

         <div
            className='ag-theme-quartz w-full h-[calc(100vh-300px)] mt-4'
            style={{ minHeight: '400px' }}
         >
            {isDataFetching ? (
               <div className='w-full h-full flex items-center justify-center'>
                  <Spinner size='xl' color='purple.500' />
               </div>
            ) : (
               <AgGridReact
                  animateRows
                  ref={gridRef}
                  rowData={rowData}
                  columnDefs={colDefs}
                  domLayout='autoHeight'
                  suppressExcelExport={true}
                  rowSelection={{ mode: 'multiRow' }}
               />
            )}
         </div>
      </div>
   );
};

export default CustomDashboard;
