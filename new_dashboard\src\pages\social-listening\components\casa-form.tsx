import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToCasa } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/casa.png';
import { casaIntegrationSteps } from '../utils/constant';
import { NewCommerceIntegrationLayout } from './seller-panel-components/new-commerce-integration-layout';
import { CasaDBConnectionPanel } from './seller-panel-components/casa-panel';

interface FormFields {
   channelName: string;
   host: string;
   port: string;
   user: string;
   password: string;
   database: string;
   tablename: string;
   schema?: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const CasaForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: 'casa',
      host: '',
      port: '',
      user: '',
      password: '',
      database: '',
      tablename: '',
      schema: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { host, port, user, password, database, tablename } = formFields;

         void (async () => {
            try {
               setTrying(true);
               setApiError({ success: false, message: '' });

               await connectDisconnectToCasa({
                  channel_name: 'casa',
                  client_id,
                  host,
                  port,
                  user,
                  password,
                  database,
                  tablename,
                  isConnect: true,
               });

               setFormFields(defaultState);
               setApiError({
                  success: true,
                  message: 'Connection Established, Redirecting...',
               });
               setTimeout(() => navigate('/integrations'), 3000);
            } catch (err) {
               let errMessage = 'Error connecting to CASA';
               if (
                  err &&
                  typeof err === 'object' &&
                  'response' in err &&
                  err.response &&
                  typeof err.response === 'object' &&
                  'data' in err.response &&
                  err.response.data &&
                  typeof err.response.data === 'object' &&
                  'message' in err.response.data
               ) {
                  errMessage =
                     (err.response.data as { message?: string }).message ||
                     errMessage;
               }
               setApiError({ success: false, message: errMessage });
            } finally {
               setTrying(false);
            }
         })();
      },
      [formFields, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   return (
      <NewCommerceIntegrationLayout
         title='CASA'
         description='Connect your CASA database to sync data seamlessly'
         logo={image}
         logoAlt='CASA Logo'
         steps={casaIntegrationSteps}
      >
         <CasaDBConnectionPanel
            title='Database Connection Details'
            description='Please provide your CASA database credentials to establish a secure connection'
            host={formFields.host}
            port={formFields.port}
            user={formFields.user}
            password={formFields.password}
            database={formFields.database}
            tablename={formFields.tablename}
            schema={formFields.schema}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText='Connect to CASA'
            loadingText='Connecting to CASA...'
         />
      </NewCommerceIntegrationLayout>
   );
};

export default CasaForm;
