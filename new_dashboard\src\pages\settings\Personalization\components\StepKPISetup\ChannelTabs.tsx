import { Flex, Button } from '@chakra-ui/react';

interface ChannelTabsProps {
  channels: string[];
  activeChannel: string;
  onSelect: (id: string) => void;
}

const ChannelTabs = ({ channels, activeChannel, onSelect }: ChannelTabsProps) => {
  return (
    <Flex gap={3} mb={4}>
      {channels.map((ch) => (
        <Button
          key={ch}
          onClick={() => onSelect(ch)}
          variant={activeChannel === ch ? 'solid' : 'outline'}
          colorScheme="purple"
          size="sm"
        >
          {ch}
        </Button>
      ))}
    </Flex>
  );
};

export default ChannelTabs;
