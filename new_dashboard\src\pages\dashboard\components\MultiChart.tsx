import { useAppSelector } from '@/store/store';
import { noZeroKPI } from '@/utils/strings/kpi-constants';
import { useColorMode } from '@chakra-ui/react';
import Chart from 'react-apexcharts';
import { ChartProp, KpiTimelineItem, OptionsMore } from '../utils/interface';
import { getFormattedVal, getFullData, toHHMMSS } from '../utils/helpers';
import { ApexOptions } from 'apexcharts';

function MultiLineChart(props: ChartProp) {
   const { kpiDetails } = props;
   if (!kpiDetails?.current_allData?.length) return null;
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
   const currFull = getFullData(dateRange, kpiDetails.current_allData);
   const prevFull = getFullData(
      prevRange,
      kpiDetails.previous_allData,
      // groupBy,
   );
   const { colorMode } = useColorMode();
   const categories = kpiDetails.current_allData.map(
      (x) => x.date.split(' ')[0],
   );
   const categories2 = kpiDetails.previous_allData.map(
      (x) => x.date.split(' ')[0],
   );
   const getLegendHeight = (labels: string[]): number => {
      return labels[0].length > 25 ? 40 : 0;
   };
   const chartData = {
      options: {
         chart: {
            id: kpiDetails.kpi_display_name,
            toolbar: {
               show: false,
            },
            zoom: {
               enabled: false,
            },
            height: 300,
         },

         stroke: {
            curve: 'smooth',
            width: [4, 3],
            dashArray: [0, 6],
         },
         xaxis: {
            tickAmount: 30,
            labels: {
               show: true,
               rotate: -45,
               rotateAlways: true,
               maxHeight: 200,
               style: {
                  colors: colorMode ? '#FFFFFF' : '#000000',
               },
            },
         },
         colors: ['#8714D2', '#f23c7fff'],
         labels: categories,

         yaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',

            labels: {
               style: {
                  colors: colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         legend: {
            horizontalAlign: 'left',
            offsetX: 0,
            // offsetY: getOffsetY(categories),
            labels: {
               colors: colorMode === 'dark' ? '#FFFFFF' : '#000000',
            },
            height: getLegendHeight(categories),
            offsetY: Math.floor(getLegendHeight(categories) / 2.5),
         },
         tooltip: {
            y: [
               {
                  formatter: function (value: number) {
                     if (!value && noZeroKPI.includes(kpiDetails.kpi_names))
                        return 'N/A';
                     return kpiDetails.kpi_unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
               {
                  formatter: function (value: number) {
                     if (!value && noZeroKPI.includes(kpiDetails.kpi_names))
                        return 'N/A';
                     return kpiDetails.kpi_unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
            ],
            x: {
               formatter: function (_v: number, x: OptionsMore) {
                  return `${categories[x.dataPointIndex]} vs ${categories2[x.dataPointIndex]}`;
               },
            },
         },
         dataLabels: {
            enabled: false,
         },
      },
      series: [
         {
            name: kpiDetails.kpi_display_name,
            data: currFull.map((x: KpiTimelineItem) =>
               Number(x.kpi_value?.toFixed(2)),
            ),
         },
         {
            name: 'Preceding Period',
            data: prevFull.map((x: KpiTimelineItem) =>
               Number(x.kpi_value?.toFixed(2)),
            ),
         },
      ],
   };

   return (
      <>
         <Chart
            options={chartData.options as ApexOptions}
            series={chartData.series}
            type='line'
            width='100%'
            height='400px'
         />
      </>
   );
}

export default MultiLineChart;
