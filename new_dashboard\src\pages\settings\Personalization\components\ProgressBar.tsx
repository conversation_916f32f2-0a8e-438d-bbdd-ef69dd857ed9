import React from 'react';
import { Flex, Box, Text, Circle, Icon } from '@chakra-ui/react';
import { CheckIcon } from '@chakra-ui/icons';

interface ProgressBarProps {
  steps: string[];
  currentStep: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ steps, currentStep }) => {
  return (
    <Flex justify="center" width="100%" mt={6} ml={14}>
      <Flex
        align="center"
        justify="space-between"
        width="100%"
        maxW="900px"
        px={8}
      >
        {steps.map((step, i) => {
          const stepNum = i + 1;
          const isCompleted = stepNum < currentStep;
          const isActive = stepNum === currentStep;
          //const isFuture = stepNum > currentStep;

          return (
            <Flex key={step} align="center" flex="1" position="relative">
              <Circle
                size="32px"
                bg={
                  isCompleted
                    ? 'purple.500'
                    : isActive
                    ? 'white'
                    : 'gray.300'
                }
                borderWidth={isActive ? '2px' : '0px'}
                borderColor={isActive ? 'purple.500' : 'transparent'}
                color={
                  isCompleted
                    ? 'white'
                    : isActive
                    ? 'purple.600'
                    : 'white'
                }
                fontWeight="semibold"
                transition="all 0.3s ease"
                flexShrink={0}
              >
                {isCompleted ? (
                  <Icon as={CheckIcon} boxSize={3.5} />
                ) : (
                  stepNum
                )}
              </Circle>

              <Text
                ml={2}
                fontSize="sm"
                color={
                  isCompleted
                    ? 'purple.600'
                    : isActive
                    ? 'purple.600'
                    : 'gray.500'
                }
                fontWeight={isActive ? 'semibold' : 'medium'}
                whiteSpace="nowrap"
                flexShrink={0}
              >
                {step}
              </Text>

              {i < steps.length - 1 && (
                <Box
                  flex="1"
                  height="2px"
                  bg={isCompleted ? 'purple.500' : 'gray.200'}
                  ml={4}
                  mr={4}
                  borderRadius="full"
                  transition="background-color 0.3s ease"
                />
              )}
            </Flex>
          );
        })}
      </Flex>
    </Flex>
  );
};

export default ProgressBar;
