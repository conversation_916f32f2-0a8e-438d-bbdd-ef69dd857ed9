import { Table, Thead, Tbody, Tr, Th, Td, Button, Input } from '@chakra-ui/react';
import { KPIBenchmark } from '../../utils/types';
import AddMonthRow from './AddMonthRow';

interface BenchmarksTableProps {
  channelId: string;
  benchmarks: KPIBenchmark[];
  onChange: (benchmarks: KPIBenchmark[]) => void;
}

const BenchmarksTable = ({ channelId, benchmarks, onChange }: BenchmarksTableProps) => {
  const headersByChannel: Record<string, string[]> = {
    google: ['Month', 'Spend', 'Conversions', 'CPC Target', 'CTR Target'],
    meta: ['Month', 'Impressions', 'Reach', 'CPM', 'CTR'],
    shopify: ['Month', 'Revenue', 'Orders', 'AOV', 'Conversion Rate'],
  };

  console.log('benchmarks', benchmarks);

  const addMonth = () => {
    onChange([...benchmarks, { month: '', spend: 0 }]);
  };

  return (
    <Table variant="simple" size="sm">
      <Thead>
        <Tr>
          {headersByChannel[channelId]?.map((h) => (
            <Th key={h}>{h}</Th>
          ))}
        </Tr>
      </Thead>

      <Tbody>
        {benchmarks.map((row, i) => (
          <Tr key={i}>
            {headersByChannel[channelId]?.map((key) => (
              <Td key={key}>
                <Input
                  size="sm"
                  value={row[key.toLowerCase().replace(/\s/g, '')] ?? ''}
                  onChange={(e) => {
                    const updated = [...benchmarks];
                    updated[i] = { ...row, [key.toLowerCase().replace(/\s/g, '')]: e.target.value };
                    onChange(updated);
                  }}
                />
              </Td>
            ))}
          </Tr>
        ))}

        <AddMonthRow onAdd={addMonth} />
      </Tbody>
    </Table>
  );
};

export default BenchmarksTable;
