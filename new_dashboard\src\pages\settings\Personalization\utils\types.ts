export interface Channel {
   id: string;
   name: string;
   icon: string;
}

export interface KPIBenchmark {
   month: string;
   [key: string]: string | number;
}

export interface ChannelKPIConfig {
   primaryKPI: string;
   secondaryKPI: string;
   weighting: {
      primary: number;
      secondary: number;
   };
   benchmarks: KPIBenchmark[];
}

export interface PersonalizationFormData {
   selectedChannels: string[];
   channelConfigs: {
      [channelId: string]: ChannelKPIConfig;
   };
}

export interface StepChannelsProps {
   data: PersonalizationFormData;
   setData: (data: PersonalizationFormData) => void;
}
