import {
  Box,
  Flex,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Text,
  Badge,
} from "@chakra-ui/react";

interface WeightingSliderProps {
  weighting: { primary: number; secondary: number };
  onChange: (value: { primary: number; secondary: number }) => void;
}

const WeightingSlider = ({ weighting, onChange }: WeightingSliderProps) => {
  const { primary, secondary } = weighting;

  const tertiary = Math.max(0, 100 - (primary + secondary));

  const handlePrimaryChange = (val: number) => {
    const newPrimary = Math.min(100 - secondary, val);
    onChange({ primary: newPrimary, secondary });
  };

  const handleSecondaryChange = (val: number) => {
    const newSecondary = Math.min(100 - primary, val);
    onChange({ primary, secondary: newSecondary });
  };

  return (
    <Box mb={8}>
      <Text mb={2} fontWeight="medium">
        Weighting
      </Text>
      <Flex align="center" justify="space-between">
        <Text fontSize="sm">Primary: {primary}%</Text>
        <Badge colorScheme="purple">Balanced</Badge>
        <Text fontSize="sm">Secondary: {secondary}%</Text>
      </Flex>

      <Box position="relative" mt={3} height="24px">
        {/* Main track */}
        <Box
          position="absolute"
          top="50%"
          transform="translateY(-50%)"
          height="4px"
          width="100%"
          bg="gray.200"
          borderRadius="full"
        />

        {/* Filled range */}
        <Box
          position="absolute"
          top="50%"
          transform="translateY(-50%)"
          left={`${primary}%`}
          right={`${secondary}%`}
          height="4px"
          bg="purple.400"
          borderRadius="full"
        />

       {/* Primary thumb */}
<Slider
  value={primary}
  onChange={handlePrimaryChange}
  min={0}
  max={100}
  step={1}
  position="absolute"
  width="100%"
  zIndex={2} // 👈 slightly below secondary to avoid blocking
  top="50%"
  transform="translateY(-12px)" // 👈 lifted slightly for perfect alignment
>
  <SliderTrack bg="transparent" pointerEvents="none">
    <SliderFilledTrack bg="transparent" />
  </SliderTrack>
  <SliderThumb
    pointerEvents="auto"
    boxSize={5}
    bg="white"
    border="3px solid #805AD5"
    borderRadius="full"
    boxShadow="0 0 4px rgba(128,90,213,0.4)"
  />
</Slider>

{/* Secondary thumb */}
<Slider
  value={100 - secondary}
  onChange={(v) => handleSecondaryChange(100 - v)}
  min={0}
  max={100}
  step={1}
  position="absolute"
  width="100%"
  zIndex={3} // 👈 higher z-index so it's clickable even near overlap
  top="50%"
  transform="translateY(-34px)" // 👈 aligned slightly lower
>
  <SliderTrack bg="transparent" pointerEvents="none">
    <SliderFilledTrack bg="transparent" />
  </SliderTrack>
  <SliderThumb
    pointerEvents="auto"
    boxSize={5}
    bg="white"
    border="3px solid #805AD5"
    borderRadius="full"
    boxShadow="0 0 4px rgba(128,90,213,0.4)"
  />
</Slider>

      </Box>

      <Text mt={2} fontSize="sm" color="gray.600">
        Tertiary automatically assigned: {tertiary}%
      </Text>
    </Box>
  );
};

export default WeightingSlider;
