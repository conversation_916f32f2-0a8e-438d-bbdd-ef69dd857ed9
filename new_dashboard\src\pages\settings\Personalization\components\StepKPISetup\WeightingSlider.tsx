import {
  Box,
  Flex,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Text,
  Badge,
} from "@chakra-ui/react";

interface WeightingSliderProps {
  weighting: { primary: number; secondary: number };
  onChange: (value: { primary: number; secondary: number }) => void;
}

const WeightingSlider = ({ weighting, onChange }: WeightingSliderProps) => {
  const { primary, secondary } = weighting;

  const tertiary = Math.max(0, 100 - (primary + secondary));

  const handlePrimaryChange = (val: number) => {
    const newPrimary = Math.min(100 - secondary, val);
    onChange({ primary: newPrimary, secondary });
  };

  const handleSecondaryChange = (val: number) => {
    const newSecondary = Math.min(100 - primary, val);
    onChange({ primary, secondary: newSecondary });
  };

  return (
    <Box mb={8}>
      <Text mb={2} fontWeight="medium">
        Weighting
      </Text>
      <Flex align="center" justify="space-between">
        <Text fontSize="sm">Primary: {primary}%</Text>
        <Badge colorScheme="purple">Balanced</Badge>
        <Text fontSize="sm">Secondary: {secondary}%</Text>
      </Flex>

      <Box position="relative" mt={3} height="60px">
        {/* Main track */}
        <Box
          position="absolute"
          top="50%"
          transform="translateY(-50%)"
          height="4px"
          width="100%"
          bg="gray.200"
          borderRadius="full"
        />

        {/* Filled range (tertiary area) */}
        <Box
          position="absolute"
          top="50%"
          transform="translateY(-50%)"
          left={`${primary}%`}
          right={`${secondary}%`}
          height="4px"
          bg="purple.400"
          borderRadius="full"
        />

        {/* Primary slider container - positioned above track */}
        <Box
          position="absolute"
          top="20%"
          width="100%"
          height="20px"
        >
          <Slider
            value={primary}
            onChange={handlePrimaryChange}
            min={0}
            max={100 - secondary}
            step={1}
            width="100%"
          >
            <SliderTrack bg="transparent" height="20px">
              <SliderFilledTrack bg="transparent" />
            </SliderTrack>
            <SliderThumb
              boxSize={5}
              bg="white"
              border="3px solid #805AD5"
              borderRadius="full"
              boxShadow="0 0 4px rgba(128,90,213,0.4)"
              _focus={{ boxShadow: "0 0 8px rgba(128,90,213,0.6)" }}
              _hover={{ transform: "scale(1.1)" }}
            />
          </Slider>
        </Box>

        {/* Secondary slider container - positioned below track */}
        <Box
          position="absolute"
          top="60%"
          width="100%"
          height="20px"
          transform="scaleX(-1)"
        >
          <Slider
            value={secondary}
            onChange={handleSecondaryChange}
            min={0}
            max={100 - primary}
            step={1}
            width="100%"
          >
            <SliderTrack bg="transparent" height="20px">
              <SliderFilledTrack bg="transparent" />
            </SliderTrack>
            <SliderThumb
              boxSize={5}
              bg="white"
              border="3px solid #805AD5"
              borderRadius="full"
              boxShadow="0 0 4px rgba(128,90,213,0.4)"
              transform="scaleX(-1)"
              _focus={{ boxShadow: "0 0 8px rgba(128,90,213,0.6)" }}
              _hover={{ transform: "scaleX(-1) scale(1.1)" }}
            />
          </Slider>
        </Box>

      </Box>

      <Text mt={2} fontSize="sm" color="gray.600">
        Tertiary automatically assigned: {tertiary}%
      </Text>
    </Box>
  );
};

export default WeightingSlider;
